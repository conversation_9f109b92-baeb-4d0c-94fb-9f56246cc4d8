<?php

namespace App\Services\PushNotification;

use App\Services\PushNotification\Contracts\PushNotificationDriverInterface;
use App\Services\PushNotification\Drivers\FirebaseDriver;
use App\Services\PushNotification\Drivers\FirebaseV1Driver;
use App\Services\PushNotification\Drivers\LogDriver;
use InvalidArgumentException;

class PushNotificationFactory
{
    /**
     * Create a push notification driver instance
     *
     * @param string $driver Driver name
     * @param array $config Driver configuration
     * @return PushNotificationDriverInterface
     * @throws InvalidArgumentException
     */
    public static function create(string $driver, array $config = []): PushNotificationDriverInterface
    {
        return match ($driver) {
            'firebase' => new FirebaseDriver(
                $config['project_id'] ?? config('services.firebase.project_id'),
                $config['service_account_path'] ?? config('services.firebase.service_account_path')
            ),
            'firebase_v1' => new FirebaseV1Driver(
                $config['project_id'] ?? config('services.firebase.project_id'),
                $config['private_key'] ?? config('services.firebase.private_key'),
                $config['client_email'] ?? config('services.firebase.client_email')
            ),
            'log' => new LogDriver(),
            default => throw new InvalidArgumentException("Unsupported push notification driver: {$driver}")
        };
    }

    /**
     * Get available drivers
     *
     * @return array
     */
    public static function getAvailableDrivers(): array
    {
        return [
            'firebase' => FirebaseDriver::class,
            'firebase_v1' => FirebaseV1Driver::class,
            'log' => LogDriver::class,
        ];
    }

    /**
     * Check if a driver is supported
     *
     * @param string $driver
     * @return bool
     */
    public static function isDriverSupported(string $driver): bool
    {
        return array_key_exists($driver, self::getAvailableDrivers());
    }
}
