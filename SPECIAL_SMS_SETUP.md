# Special SMS FCM Notification Setup

This document provides setup instructions for the `/sendSpecialSms` endpoint that sends signed Firebase Cloud Messaging notifications for your Flutter app.

## Overview

The `/sendSpecialSms` endpoint has been added to your existing Laravel backend. It:

1. Accepts `deviceToken`, `phoneNumber`, and `message` parameters
2. Generates a Unix timestamp
3. Creates an HMAC-SHA256 signature using: `phoneNumber + message + timestamp`
4. Sends an FCM notification with a data payload containing the signed information
5. Returns the FCM response, signature, and timestamp

## Configuration

### 1. Environment Variables

Add this to your `.env` file:

```env
# Special SMS Secret Key (must match your Flutter app)
SPECIAL_SMS_SECRET="superSecretKey123"
```

**Important**: Make sure this secret key matches exactly what you use in your Flutter app for signature verification.

### 2. Firebase Configuration

Your Firebase configuration is already set up. The endpoint uses your existing Firebase setup:

```env
PUSH_NOTIFICATION_DRIVER=firebase_v1
FIREBASE_PROJECT_ID=sms-divert-app
FIREBASE_CLIENT_EMAIL="<EMAIL>"
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
```

## API Endpoint

### URL
```
POST /api/sendSpecialSms
```

### Headers
```
Content-Type: application/json
Authorization: Bearer YOUR_APPLICATION_TOKEN
```

### Request Body
```json
{
    "deviceToken": "string (required) - FCM device token",
    "phoneNumber": "string (required) - Phone number sending the message",
    "message": "string (required) - Message content (max 1000 chars)"
}
```

### Response Format

#### Success Response (200)
```json
{
    "success": true,
    "message": "Special SMS notification sent successfully",
    "data": {
        "fcmResponse": {
            "success": true,
            "message_id": "projects/sms-divert-app/messages/0:1234567890123456%abcdef",
            "driver": "firebase_v1"
        },
        "signature": "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
        "timestamp": 1692123456,
        "phoneNumber": "+1234567890",
        "messageLength": 25
    }
}
```

#### Error Response (400/500)
```json
{
    "success": false,
    "message": "Failed to send special SMS notification",
    "error": "Invalid device token"
}
```

## FCM Data Payload

The FCM notification includes this data payload:

```json
{
    "type": "special_sms",
    "phoneNumber": "+1234567890",
    "message": "Your message content here",
    "timestamp": "1692123456",
    "signature": "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"
}
```

## Testing

### 1. Sample cURL Request

```bash
curl -X POST http://localhost:8000/api/sendSpecialSms \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer MYAPP" \
  -d '{
    "deviceToken": "your-fcm-device-token-here",
    "phoneNumber": "+1234567890",
    "message": "This is a test special SMS message"
  }'
```

### 2. Test with Postman

1. **Method**: POST
2. **URL**: `http://localhost:8000/api/sendSpecialSms`
3. **Headers**:
   - `Content-Type: application/json`
   - `Authorization: Bearer MYAPP`
4. **Body** (raw JSON):
```json
{
    "deviceToken": "your-fcm-device-token-here",
    "phoneNumber": "+1234567890",
    "message": "This is a test special SMS message"
}
```

### 3. Getting a Test Device Token

To get a device token for testing:

1. In your Flutter app, use `FirebaseMessaging.instance.getToken()`
2. Print or log the token
3. Use that token in your API requests

## Signature Verification in Flutter

In your Flutter app, verify the signature like this:

```dart
import 'dart:convert';
import 'package:crypto/crypto.dart';

bool verifySignature(String phoneNumber, String message, String timestamp, String signature) {
  const String secretKey = 'superSecretKey123'; // Must match backend
  
  String data = phoneNumber + message + timestamp;
  var key = utf8.encode(secretKey);
  var bytes = utf8.encode(data);
  
  var hmacSha256 = Hmac(sha256, key);
  var digest = hmacSha256.convert(bytes);
  
  return digest.toString() == signature;
}
```

## Error Handling

Common error scenarios:

1. **Invalid device token**: Returns 400 with "Invalid device token"
2. **Missing parameters**: Returns 422 with validation errors
3. **Firebase service unavailable**: Returns 500 with error details
4. **Invalid application token**: Returns 401 Unauthorized

## Security Notes

1. **Secret Key**: Keep your `SPECIAL_SMS_SECRET` secure and never expose it in client-side code
2. **HTTPS**: Always use HTTPS in production
3. **Token Validation**: The endpoint validates the application token via middleware
4. **Input Validation**: All inputs are validated before processing

## Integration with Existing System

This endpoint integrates seamlessly with your existing Laravel notification system:

- Uses the same Firebase configuration
- Leverages existing `PushNotificationService`
- Follows the same authentication middleware pattern
- Maintains consistent error handling and logging

The endpoint is now available at `/api/sendSpecialSms` and ready for use with your Flutter app!
