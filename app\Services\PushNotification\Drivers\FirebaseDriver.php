<?php

namespace App\Services\PushNotification\Drivers;

use App\Services\PushNotification\Contracts\PushNotificationDriverInterface;
use Google\Auth\ApplicationDefaultCredentials;
use Google\Auth\Credentials\ServiceAccountCredentials;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FirebaseDriver implements PushNotificationDriverInterface
{
    protected string $projectId;
    protected string $serviceAccountPath;
    protected string $fcmUrl;
    protected ?string $accessToken = null;
    protected ?int $tokenExpiry = null;

    public function __construct(string $projectId, string $serviceAccountPath)
    {
        $this->projectId = $projectId;
        $this->serviceAccountPath = $serviceAccountPath;
        $this->fcmUrl = "https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send";
    }

    /**
     * Send a push notification to a single device
     */
    public function sendToDevice(string $token, string $title, string $body, array $data = []): array
    {
        $message = [
            'message' => [
                'token' => $token,
                'notification' => [
                    'title' => $title,
                    'body' => $body,
                ],
                'data' => $this->formatDataPayload($data),
                'android' => [
                    'priority' => 'high',
                    'notification' => [
                        'sound' => 'default',
                    ],
                ],
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'sound' => 'default',
                        ],
                    ],
                ],
            ],
        ];

        return $this->sendRequest($message);
    }

    /**
     * Send a push notification to multiple devices
     */
    public function sendToMultipleDevices(array $tokens, string $title, string $body, array $data = []): array
    {
        // HTTP v1 API doesn't support multiple tokens in a single request
        // We need to send individual requests for each token
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($tokens as $token) {
            $result = $this->sendToDevice($token, $title, $body, $data);
            $results[] = $result;

            if ($result['success']) {
                $successCount++;
            } else {
                $failureCount++;
            }
        }

        return [
            'success' => $successCount > 0,
            'data' => [
                'results' => $results,
                'success_count' => $successCount,
                'failure_count' => $failureCount,
                'total_count' => count($tokens),
            ],
            'driver' => $this->getDriverName(),
        ];
    }

    /**
     * Send a push notification to a topic
     */
    public function sendToTopic(string $topic, string $title, string $body, array $data = []): array
    {
        $message = [
            'message' => [
                'topic' => $topic,
                'notification' => [
                    'title' => $title,
                    'body' => $body,
                ],
                'data' => $this->formatDataPayload($data),
                'android' => [
                    'priority' => 'high',
                    'notification' => [
                        'sound' => 'default',
                    ],
                ],
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'sound' => 'default',
                        ],
                    ],
                ],
            ],
        ];

        return $this->sendRequest($message);
    }

    /**
     * Validate if a device token is valid
     */
    public function validateToken(string $token): bool
    {
        // Firebase tokens are typically 152+ characters long
        return !empty($token) && strlen($token) >= 140;
    }

    /**
     * Get the driver name
     */
    public function getDriverName(): string
    {
        return 'firebase';
    }

    /**
     * Get OAuth 2.0 access token for Firebase HTTP v1 API
     */
    protected function getAccessToken(): string
    {
        // Check if we have a valid cached token
        if ($this->accessToken && $this->tokenExpiry && time() < $this->tokenExpiry) {
            return $this->accessToken;
        }

        try {
            // Load service account credentials
            $credentials = new ServiceAccountCredentials(
                'https://www.googleapis.com/auth/firebase.messaging',
                json_decode(file_get_contents($this->serviceAccountPath), true)
            );

            // Get access token
            $token = $credentials->fetchAuthToken();

            $this->accessToken = $token['access_token'];
            $this->tokenExpiry = time() + ($token['expires_in'] ?? 3600) - 60; // 1 minute buffer

            return $this->accessToken;
        } catch (\Exception $e) {
            Log::error('Failed to get Firebase access token', [
                'message' => $e->getMessage(),
                'service_account_path' => $this->serviceAccountPath
            ]);
            throw new \Exception('Failed to authenticate with Firebase: ' . $e->getMessage());
        }
    }

    /**
     * Format data payload to ensure all values are strings (required by FCM)
     */
    protected function formatDataPayload(array $data): array
    {
        $formatted = [];
        foreach ($data as $key => $value) {
            $formatted[$key] = is_string($value) ? $value : json_encode($value);
        }
        return $formatted;
    }

    /**
     * Send HTTP request to Firebase FCM v1 API
     */
    protected function sendRequest(array $message): array
    {
        try {
            $accessToken = $this->getAccessToken();

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ])->post($this->fcmUrl, $message);

            $responseData = $response->json();

            if ($response->successful()) {
                Log::info('Firebase notification sent successfully', [
                    'response' => $responseData,
                    'message' => $message
                ]);

                return [
                    'success' => true,
                    'data' => $responseData,
                    'driver' => $this->getDriverName(),
                ];
            } else {
                Log::error('Firebase notification failed', [
                    'status' => $response->status(),
                    'response' => $responseData,
                    'message' => $message
                ]);

                return [
                    'success' => false,
                    'error' => $responseData['error']['message'] ?? 'Unknown error',
                    'driver' => $this->getDriverName(),
                ];
            }
        } catch (\Exception $e) {
            Log::error('Firebase notification exception', [
                'message' => $e->getMessage(),
                'payload' => $message
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'driver' => $this->getDriverName(),
            ];
        }
    }
}
