<?php

namespace App\Http\Controllers;

use App\Facades\PushNotification;
use App\Models\User;
use App\Services\PushNotification\PushNotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class NotificationController extends Controller
{
    protected PushNotificationService $pushNotificationService;

    public function __construct(PushNotificationService $pushNotificationService)
    {
        $this->pushNotificationService = $pushNotificationService;
    }

    /**
     * Send notification to a specific user
     */
    public function sendToUser(Request $request): JsonResponse
    {
        $request->validate([
            'user_phone' => 'required|exists:users,phone',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'data' => 'sometimes|array',
        ]);

        $user = User::where('phone', $request->user_phone)->firstOrFail();

        $result = $this->pushNotificationService->sendToUser(
            $user,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Notification sent successfully' : 'Failed to send notification',
            'data' => $result,
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Send notification to multiple users
     */
    public function sendToUsers(Request $request): JsonResponse
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'data' => 'sometimes|array',
        ]);

        $users = User::whereIn('id', $request->user_ids)->get();

        $result = $this->pushNotificationService->sendToUsers(
            $users,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Notifications sent successfully' : 'Failed to send notifications',
            'data' => $result,
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Send notification to a device token directly
     */
    public function sendToToken(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required|string',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'data' => 'sometimes|array',
        ]);

        $result = $this->pushNotificationService->sendToToken(
            $request->token,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Notification sent successfully' : 'Failed to send notification',
            'data' => $result,
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Send notification to a topic
     */
    public function sendToTopic(Request $request): JsonResponse
    {
        $request->validate([
            'topic' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'data' => 'sometimes|array',
        ]);

        $result = $this->pushNotificationService->sendToTopic(
            $request->topic,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Notification sent successfully' : 'Failed to send notification',
            'data' => $result,
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Validate a notification token
     */
    public function validateToken(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required|string',
        ]);

        $isValid = $this->pushNotificationService->validateToken($request->token);

        return response()->json([
            'valid' => $isValid,
            'message' => $isValid ? 'Token is valid' : 'Token is invalid',
        ]);
    }

    /**
     * Send special SMS notification with HMAC signature
     */
    public function sendSpecialSms(Request $request): JsonResponse
    {
        $request->validate([
            'sender_number' => 'required|string|exists:users,phone',
            'phoneNumber' => 'required|string',
            'message' => 'required|string|max:1000',
        ]);

        try {
            // Generate timestamp (Unix seconds)
            $timestamp = time();

            // Get secret key from config (should match Flutter app)
            $secretKey = config('app.special_sms_secret', 'superSecretKey123');

            // Generate HMAC-SHA256 signature
            $data = $request->phoneNumber . $request->message . $timestamp;
            $signature = hash_hmac('sha256', $data, $secretKey);

            // Prepare data payload for FCM
            $fcmData = [
                'type' => 'special_sms',
                'test' => 'somethig',
                'phoneNumber' => $request->phoneNumber,
                'message' => $request->message,
                'timestamp' => (string) $timestamp,
                'signature' => $signature,
            ];

            // Send FCM notification using existing service
            $user = User::where('phone', $request->sender_number)->firstOrFail();
            $result = $this->pushNotificationService->sendToToken(
                $user->notif_token,
                'Special SMS', // title
                "Message from {$request->phoneNumber}", // body
                $fcmData // data payload
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Special SMS notification sent successfully',
                    'data' => [
                        'fcmResponse' => $result,
                        'signature' => $signature,
                        'timestamp' => $timestamp,
                        'phoneNumber' => $request->phoneNumber,
                        'messageLength' => strlen($request->message),
                    ],
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send special SMS notification',
                    'error' => $result['error'] ?? 'Unknown error',
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send special SMS notification',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Example using the facade
     */
    public function sendUsingFacade(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'data' => 'sometimes|array',
        ]);

        $user = User::findOrFail($request->user_id);

        $result = PushNotification::sendToUser(
            $user,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Notification sent successfully via facade' : 'Failed to send notification',
            'data' => $result,
        ], $result['success'] ? 200 : 400);
    }
}
