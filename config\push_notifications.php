<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Push Notification Driver
    |--------------------------------------------------------------------------
    |
    | This option controls the default push notification driver that will be
    | used to send notifications. You may set this to any of the drivers
    | defined in the "drivers" array below.
    |
    | Supported: "firebase", "firebase_v1", "log"
    |
    */

    'default' => env('PUSH_NOTIFICATION_DRIVER', 'firebase_v1'),

    /*
    |--------------------------------------------------------------------------
    | Push Notification Drivers
    |--------------------------------------------------------------------------
    |
    | Here you may configure the push notification drivers for your application.
    | Each driver has its own configuration options.
    |
    */

    'drivers' => [

        'firebase' => [
            'project_id' => env('FIREBASE_PROJECT_ID'),
            'service_account_path' => env('FIREBASE_SERVICE_ACCOUNT_PATH', storage_path('app/firebase-service-account.json')),
        ],

        'firebase_v1' => [
            'project_id' => env('FIREBASE_PROJECT_ID'),
            'private_key' => env('FIREBASE_PRIVATE_KEY'),
            'client_email' => env('FIREBASE_CLIENT_EMAIL'),
        ],

        'log' => [
            // Log driver doesn't require any configuration
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Push Notification Options
    |--------------------------------------------------------------------------
    |
    | Here you may configure additional options for push notifications.
    |
    */

    'options' => [
        
        // Maximum number of tokens to send in a single batch
        'max_batch_size' => 1000,
        
        // Default notification priority
        'default_priority' => 'high',
        
        // Default sound for notifications
        'default_sound' => 'default',
        
        // Enable/disable notification logging
        'log_notifications' => env('LOG_PUSH_NOTIFICATIONS', true),
        
    ],

];
