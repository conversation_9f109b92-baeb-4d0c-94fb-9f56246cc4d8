<?php

namespace App\Http\Controllers;

use App\Models\ReceivedMessage;
use App\Models\User;
use Illuminate\Http\Request;
use Log;

class UserController extends Controller
{


    /**
     * Create a new user
     */
    public function store(Request $request)
    {
        $request->validate([
            'phone' => 'required|string|unique:users,phone',
            'notif_token' => 'required|string',
            'endpoint' => 'required|string',
        ]);

        $user = User::updateOrCreate([
            'phone' => $request->phone,
        ], [
            'notif_token' => $request->notif_token,
            'endpoint' => $request->endpoint,
        ]);

        return response()->json([
            'success' => true,
            'data' => $user
        ], 201);
    }

    public function gotNotif(Request $request)
    {
        Log::info('Got notif', $request->all());
        $request->validate([
            'phone' => 'required|string',
            'sender'=> 'required|string',
            'message' => 'required|string',
        ]);

        ReceivedMessage::create([
            'sender' => $request->sender,
            'receiver' => $request->phone,
            'message' => $request->message,
        ]);

        return response()->json([
            'success' => true,
            'data' => 'Got notif'
        ], 200);
    }
}
