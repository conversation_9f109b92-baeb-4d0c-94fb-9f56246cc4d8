# Push Notification System

This Laravel application includes a comprehensive push notification system built using the **Strategy Pattern** and **Factory Pattern** with Firebase HTTP v1 API as the default driver.

## Architecture

### Design Patterns Used

1. **Strategy Pattern**: Different notification drivers (Firebase V1, Firebase Legacy, Log) implement the same interface
2. **Factory Pattern**: Creates appropriate driver instances based on configuration

### Components

- **PushNotificationDriverInterface**: Contract defining notification driver methods
- **FirebaseV1Driver**: Modern Firebase Cloud Messaging HTTP v1 API implementation (recommended)
- **FirebaseDriver**: Legacy Firebase implementation using service account files
- **LogDriver**: Development/testing driver that logs notifications
- **PushNotificationFactory**: Creates driver instances
- **PushNotificationService**: Main service class with high-level methods
- **PushNotificationServiceProvider**: Laravel service provider
- **PushNotification Facade**: Easy access to the service

## Configuration

### Environment Variables

Add these to your `.env` file:

**For Firebase V1 Driver (Recommended):**
```env
PUSH_NOTIFICATION_DRIVER=firebase_v1
FIREBASE_PROJECT_ID=your-firebase-project-id-here
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
LOG_PUSH_NOTIFICATIONS=true
```

**For Firebase Legacy Driver:**
```env
PUSH_NOTIFICATION_DRIVER=firebase
FIREBASE_PROJECT_ID=your-firebase-project-id-here
FIREBASE_SERVICE_ACCOUNT_PATH=storage/app/firebase-service-account.json
LOG_PUSH_NOTIFICATIONS=true
```

### Configuration File

The system uses `config/push_notifications.php` for configuration:

```php
'default' => env('PUSH_NOTIFICATION_DRIVER', 'firebase_v1'),
'drivers' => [
    'firebase_v1' => [
        'project_id' => env('FIREBASE_PROJECT_ID'),
        'private_key' => env('FIREBASE_PRIVATE_KEY'),
        'client_email' => env('FIREBASE_CLIENT_EMAIL'),
    ],
    'firebase' => [
        'project_id' => env('FIREBASE_PROJECT_ID'),
        'service_account_path' => env('FIREBASE_SERVICE_ACCOUNT_PATH'),
    ],
    'log' => [],
],
```

## Usage Examples

### Using Dependency Injection

```php
use App\Services\PushNotification\PushNotificationService;
use App\Models\User;

class SomeController extends Controller
{
    public function __construct(
        private PushNotificationService $pushNotificationService
    ) {}

    public function sendNotification()
    {
        $user = User::find(1);
        
        $result = $this->pushNotificationService->sendToUser(
            $user,
            'Hello!',
            'This is a test notification',
            ['custom_data' => 'value']
        );
        
        if ($result['success']) {
            // Notification sent successfully
        }
    }
}
```

### Using the Facade

```php
use App\Facades\PushNotification;
use App\Models\User;

// Send to a single user
$user = User::find(1);
$result = PushNotification::sendToUser($user, 'Title', 'Body', ['data' => 'value']);

// Send to multiple users
$users = User::whereIn('id', [1, 2, 3])->get();
$result = PushNotification::sendToUsers($users, 'Title', 'Body');

// Send to device token directly
$result = PushNotification::sendToToken('device_token', 'Title', 'Body');

// Send to topic
$result = PushNotification::sendToTopic('news', 'Breaking News', 'Something happened');

// Validate token
$isValid = PushNotification::validateToken('device_token');
```

### Switching Drivers

```php
// Switch to log driver for testing
$service = app(PushNotificationService::class);
$service->switchDriver('log');

// Or with custom config
$service->switchDriver('firebase', [
    'server_key' => 'different-key'
]);
```

## API Endpoints

The system includes these API endpoints:

- `POST /api/notifications/send-to-user` - Send to specific user
- `POST /api/notifications/send-to-users` - Send to multiple users  
- `POST /api/notifications/send-to-token` - Send to device token
- `POST /api/notifications/send-to-topic` - Send to topic
- `POST /api/notifications/validate-token` - Validate token
- `POST /api/notifications/send-using-facade` - Example using facade

### Example API Request

```bash
curl -X POST http://localhost:8000/api/notifications/send-to-user \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-app-token" \
  -d '{
    "user_id": 1,
    "title": "Hello!",
    "body": "This is a test notification",
    "data": {
      "action": "open_screen",
      "screen": "home"
    }
  }'
```

## Adding New Drivers

To add a new notification driver:

1. Create a new driver class implementing `PushNotificationDriverInterface`
2. Add the driver to `PushNotificationFactory::create()` method
3. Add configuration in `config/push_notifications.php`

Example:

```php
// app/Services/PushNotification/Drivers/CustomDriver.php
class CustomDriver implements PushNotificationDriverInterface
{
    public function sendToDevice(string $token, string $title, string $body, array $data = []): array
    {
        // Implementation
    }
    
    // ... other methods
}

// In PushNotificationFactory
public static function create(string $driver, array $config = []): PushNotificationDriverInterface
{
    return match ($driver) {
        'firebase' => new FirebaseDriver($config['server_key']),
        'log' => new LogDriver(),
        'custom' => new CustomDriver($config), // Add this
        default => throw new InvalidArgumentException("Unsupported driver: {$driver}")
    };
}
```

## Testing

For testing, use the log driver:

```php
// In your test
config(['push_notifications.default' => 'log']);

// Or switch driver in test
$service = app(PushNotificationService::class);
$service->switchDriver('log');
```

## Firebase Setup

### For Firebase V1 Driver (Recommended)

1. Create a Firebase project at https://console.firebase.google.com/
2. Go to Project Settings > Service Accounts
3. Click "Generate new private key" to download the service account JSON file
4. Extract the following values from the JSON file:
   - `project_id` → `FIREBASE_PROJECT_ID`
   - `private_key` → `FIREBASE_PRIVATE_KEY` (keep the \n characters)
   - `client_email` → `FIREBASE_CLIENT_EMAIL`
5. Add these to your `.env` file

### For Firebase Legacy Driver

1. Create a Firebase project
2. Go to Project Settings > Service Accounts
3. Download the service account JSON file
4. Place it in `storage/app/firebase-service-account.json`
5. Set `FIREBASE_SERVICE_ACCOUNT_PATH` in your `.env` file

### Enable Firebase Cloud Messaging

1. In your Firebase project, go to Project Settings > Cloud Messaging
2. Make sure Firebase Cloud Messaging API is enabled
3. Note your project ID for the configuration

## Error Handling

All methods return an array with this structure:

```php
[
    'success' => true|false,
    'data' => [...], // Response data on success
    'error' => 'Error message', // Error message on failure
    'driver' => 'driver_name'
]
```
