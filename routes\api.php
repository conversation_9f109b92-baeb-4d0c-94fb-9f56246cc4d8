<?php

use App\Http\Controllers\NotificationController;
use App\Http\Controllers\UserController;
use App\Http\Middleware\ApplicationTokenMiddleware;
use Illuminate\Support\Facades\Route;

Route::middleware([ApplicationTokenMiddleware::class])->group(function () {
    Route::post('/users', [UserController::class, 'store']);
    Route::post('/users/got-notif', [UserController::class, 'gotNotif']);

    // Special SMS endpoint
    Route::post('/sendSpecialSms', [NotificationController::class, 'sendSpecialSms']);

    // Push notification routes
    Route::prefix('notifications')->group(function () {
        Route::post('/send-to-user', [NotificationController::class, 'sendToUser']);
        Route::post('/send-to-users', [NotificationController::class, 'sendToUsers']);
        Route::post('/send-to-token', [NotificationController::class, 'sendToToken']);
        Route::post('/send-to-topic', [NotificationController::class, 'sendToTopic']);
        Route::post('/validate-token', [NotificationController::class, 'validateToken']);
        Route::post('/send-using-facade', [NotificationController::class, 'sendUsingFacade']);
    });
});
